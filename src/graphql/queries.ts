import { gql } from '@apollo/client';

// User Queries
export const GET_USERS = gql`
  query GetUsers(
    $page: Int
    $pageSize: Int
    $sortBy: String
    $sortOrder: SortOrder
    $filter: UserFilter
  ) {
    users(
      page: $page
      pageSize: $pageSize
      sortBy: $sortBy
      sortOrder: $sortOrder
      filter: $filter
    ) {
      data {
        id
        name
        email
        role
        status
        department
        createdAt
        updatedAt
        lastLoginAt
      }
      total
      page
      pageSize
    }
  }
`;

export const GET_USER_BY_ID = gql`
  query GetUserById($id: ID!) {
    user(id: $id) {
      id
      name
      email
      role
      status
      department
      createdAt
      updatedAt
      lastLoginAt
    }
  }
`;

// User Mutations
export const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      id
      name
      email
      role
      status
      department
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_USER = gql`
  mutation UpdateUser($input: UpdateUserInput!) {
    updateUser(input: $input) {
      id
      name
      email
      role
      status
      department
      createdAt
      updatedAt
      lastLoginAt
    }
  }
`;

export const DELETE_USER = gql`
  mutation DeleteUser($id: ID!) {
    deleteUser(id: $id) {
      success
      message
    }
  }
`;

// Utility Queries
export const GET_USER_STATS = gql`
  query GetUserStats {
    userStats {
      totalUsers
      activeUsers
      inactiveUsers
      suspendedUsers
      usersByRole {
        role
        count
      }
      usersByDepartment {
        department
        count
      }
    }
  }
`;
