// GraphQL Types
export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  status: UserStatus;
  createdAt: string;
  updatedAt: string;
  department?: string;
  lastLoginAt?: string;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  MODERATOR = 'MODERATOR',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

export interface UsersQueryResult {
  users: {
    data: User[];
    total: number;
    page: number;
    pageSize: number;
  };
}

export interface UsersQueryVariables {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  filter?: UserFilter;
}

export interface UserFilter {
  name?: string;
  email?: string;
  role?: UserRole;
  status?: UserStatus;
  department?: string;
}

export interface CreateUserInput {
  name: string;
  email: string;
  role: UserRole;
  department?: string;
}

export interface UpdateUserInput {
  id: string;
  name?: string;
  email?: string;
  role?: UserRole;
  status?: UserStatus;
  department?: string;
}

// Table sorting and filtering types
export interface SortConfig {
  key: keyof User;
  direction: 'ASC' | 'DESC';
}

export interface FilterConfig {
  [key: string]: string | UserRole | UserStatus | undefined;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}
