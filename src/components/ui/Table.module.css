.tableContainer {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.th {
  background-color: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
  position: relative;
}

.th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.th.sortable:hover {
  background-color: #e5e7eb;
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.sortIcon {
  font-size: 12px;
  color: #6b7280;
  min-width: 16px;
  text-align: center;
}

.filterRow {
  background-color: #f8f9fa;
}

.filterCell {
  padding: 8px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.filterInput {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  transition: border-color 0.2s;
}

.filterInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.filterInput::placeholder {
  color: #9ca3af;
}

.td {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  vertical-align: top;
}

.row {
  transition: background-color 0.2s;
}

.row:hover {
  background-color: #f9fafb;
}

.row:last-child .td {
  border-bottom: none;
}

.emptyCell {
  padding: 40px 16px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  border-bottom: none;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .table {
    font-size: 12px;
  }
  
  .th,
  .td {
    padding: 8px 12px;
  }
  
  .filterInput {
    font-size: 11px;
    padding: 4px 6px;
  }
}
