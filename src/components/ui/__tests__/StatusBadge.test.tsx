import React from 'react';
import { render, screen } from '@testing-library/react';
import { StatusBadge } from '../StatusBadge';
import { UserStatus, UserRole } from '../../../types/graphql';

describe('StatusBadge Component', () => {
  describe('User Status badges', () => {
    it('renders active status correctly', () => {
      render(<StatusBadge status={UserStatus.ACTIVE} type="status" />);
      
      const badge = screen.getByText('Active');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('success');
    });

    it('renders inactive status correctly', () => {
      render(<StatusBadge status={UserStatus.INACTIVE} type="status" />);
      
      const badge = screen.getByText('Inactive');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('warning');
    });

    it('renders suspended status correctly', () => {
      render(<StatusBadge status={UserStatus.SUSPENDED} type="status" />);
      
      const badge = screen.getByText('Suspended');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('danger');
    });
  });

  describe('User Role badges', () => {
    it('renders admin role correctly', () => {
      render(<StatusBadge status={UserRole.ADMIN} type="role" />);
      
      const badge = screen.getByText('Admin');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('primary');
    });

    it('renders moderator role correctly', () => {
      render(<StatusBadge status={UserRole.MODERATOR} type="role" />);
      
      const badge = screen.getByText('Moderator');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('info');
    });

    it('renders user role correctly', () => {
      render(<StatusBadge status={UserRole.USER} type="role" />);
      
      const badge = screen.getByText('User');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('default');
    });
  });

  it('applies custom className', () => {
    render(
      <StatusBadge 
        status={UserStatus.ACTIVE} 
        type="status" 
        className="custom-badge" 
      />
    );
    
    const badge = screen.getByText('Active');
    expect(badge).toHaveClass('custom-badge');
  });

  it('defaults to status type when type is not specified', () => {
    render(<StatusBadge status={UserStatus.ACTIVE} />);
    
    const badge = screen.getByText('Active');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('success');
  });

  it('handles unknown status gracefully', () => {
    render(<StatusBadge status={'UNKNOWN' as UserStatus} type="status" />);
    
    const badge = screen.getByText('UNKNOWN');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('default');
  });
});
