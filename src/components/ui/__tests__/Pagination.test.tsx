import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Pagination } from '../Pagination';

describe('Pagination Component', () => {
  const mockOnPageChange = jest.fn();
  const mockOnPageSizeChange = jest.fn();

  const defaultProps = {
    currentPage: 1,
    totalPages: 5,
    totalItems: 50,
    pageSize: 10,
    onPageChange: mockOnPageChange,
    onPageSizeChange: mockOnPageSizeChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders pagination info correctly', () => {
    render(<Pagination {...defaultProps} />);

    expect(screen.getByText('Showing 1 to 10 of 50 entries')).toBeInTheDocument();
  });

  it('renders page size selector', () => {
    render(<Pagination {...defaultProps} />);

    const select = screen.getByDisplayValue('10 per page');
    expect(select).toBeInTheDocument();
  });

  it('handles page size change', () => {
    render(<Pagination {...defaultProps} />);

    const select = screen.getByDisplayValue('10 per page');
    fireEvent.change(select, { target: { value: '25' } });

    expect(mockOnPageSizeChange).toHaveBeenCalledWith(25);
  });

  it('handles page navigation', () => {
    render(<Pagination {...defaultProps} currentPage={3} />);

    const nextButton = screen.getByLabelText('Next page');
    const prevButton = screen.getByLabelText('Previous page');

    fireEvent.click(nextButton);
    expect(mockOnPageChange).toHaveBeenCalledWith(4);

    fireEvent.click(prevButton);
    expect(mockOnPageChange).toHaveBeenCalledWith(2);
  });

  it('disables previous button on first page', () => {
    render(<Pagination {...defaultProps} currentPage={1} />);

    const prevButton = screen.getByLabelText('Previous page');
    expect(prevButton).toBeDisabled();
  });

  it('disables next button on last page', () => {
    render(<Pagination {...defaultProps} currentPage={5} />);

    const nextButton = screen.getByLabelText('Next page');
    expect(nextButton).toBeDisabled();
  });

  it('highlights current page', () => {
    render(<Pagination {...defaultProps} currentPage={3} />);

    const currentPageButton = screen.getByLabelText('Go to page 3');
    expect(currentPageButton).toHaveAttribute('aria-current', 'page');
  });

  it('handles direct page navigation', () => {
    render(<Pagination {...defaultProps} currentPage={1} />);

    const pageButton = screen.getByLabelText('Go to page 2');
    fireEvent.click(pageButton);

    expect(mockOnPageChange).toHaveBeenCalledWith(2);
  });

  it('shows ellipsis for large page ranges', () => {
    render(<Pagination {...defaultProps} totalPages={20} currentPage={10} />);

    const ellipsis = screen.getAllByText('...');
    expect(ellipsis.length).toBeGreaterThan(0);
  });

  it('does not render when totalPages is 1 or less', () => {
    const { container } = render(<Pagination {...defaultProps} totalPages={1} />);
    expect(container.firstChild).toBeNull();
  });

  it('calculates correct item range for last page', () => {
    render(<Pagination {...defaultProps} currentPage={5} totalItems={47} />);

    expect(screen.getByText('Showing 41 to 47 of 47 entries')).toBeInTheDocument();
  });
});
