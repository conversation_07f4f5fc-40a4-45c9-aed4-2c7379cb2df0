import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Table, Column } from '../Table';

interface TestData {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive';
}

const mockData: TestData[] = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', status: 'active' },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', status: 'inactive' },
  { id: '3', name: '<PERSON>', email: '<EMAIL>', status: 'active' },
];

const mockColumns: Column<TestData>[] = [
  { key: 'name', header: 'Name', sortable: true, filterable: true },
  { key: 'email', header: 'Email', sortable: true, filterable: true },
  { key: 'status', header: 'Status', sortable: false, filterable: false },
];

describe('Table Component', () => {
  const mockOnSort = jest.fn();
  const mockOnFilter = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders table with data correctly', () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(
      <Table
        data={[]}
        columns={mockColumns}
        loading={true}
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows empty message when no data', () => {
    render(
      <Table
        data={[]}
        columns={mockColumns}
        emptyMessage="No users found"
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    expect(screen.getByText('No users found')).toBeInTheDocument();
  });

  it('handles sorting when column header is clicked', async () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    const nameHeader = screen.getByText('Name').closest('th');
    fireEvent.click(nameHeader!);

    expect(mockOnSort).toHaveBeenCalledWith({
      key: 'name',
      direction: 'ASC',
    });
  });

  it('handles filtering when filter input changes', async () => {
    const user = userEvent.setup();
    
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    const nameFilter = screen.getByPlaceholderText('Filter name...');
    await user.type(nameFilter, 'John');

    await waitFor(() => {
      expect(mockOnFilter).toHaveBeenCalledWith({
        name: 'John',
      });
    });
  });

  it('displays sort icons correctly', () => {
    render(
      <Table
        data={mockData}
        columns={mockColumns}
        sortConfig={{ key: 'name', direction: 'ASC' }}
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    const nameHeader = screen.getByText('Name').closest('th');
    expect(nameHeader).toHaveTextContent('↑');
  });

  it('renders custom cell content', () => {
    const customColumns: Column<TestData>[] = [
      {
        key: 'name',
        header: 'Name',
        render: (value, row) => <strong data-testid="custom-name">{value}</strong>,
      },
    ];

    render(
      <Table
        data={mockData}
        columns={customColumns}
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    expect(screen.getByTestId('custom-name')).toHaveTextContent('John Doe');
  });

  it('applies custom className', () => {
    const { container } = render(
      <Table
        data={mockData}
        columns={mockColumns}
        className="custom-table"
        onSort={mockOnSort}
        onFilter={mockOnFilter}
      />
    );

    expect(container.querySelector('.custom-table')).toBeInTheDocument();
  });
});
