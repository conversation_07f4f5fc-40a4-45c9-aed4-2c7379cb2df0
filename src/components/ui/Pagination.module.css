.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.info {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #6b7280;
  font-size: 14px;
}

.pageSizeSelect {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.pageSizeSelect:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pageButton {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pageButton:hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.pageButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9fafb;
}

.pageButton.active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.pageButton.active:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

.navButton {
  font-weight: 500;
}

.dots {
  padding: 8px 4px;
  color: #9ca3af;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

/* Responsive design */
@media (max-width: 768px) {
  .paginationContainer {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .info {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .pagination {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .pageButton {
    padding: 6px 10px;
    font-size: 13px;
    min-width: 36px;
  }
  
  .navButton {
    padding: 6px 8px;
  }
}
