'use client';

import React, { useState, useMemo } from 'react';
import { SortConfig, FilterConfig } from '../../types/graphql';
import styles from './Table.module.css';

export interface Column<T> {
  key: keyof T;
  header: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
  width?: string;
}

interface TableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  onSort?: (sortConfig: SortConfig) => void;
  onFilter?: (filterConfig: FilterConfig) => void;
  sortConfig?: SortConfig;
  filterConfig?: FilterConfig;
  emptyMessage?: string;
  className?: string;
}

export function Table<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  onSort,
  onFilter,
  sortConfig,
  filterConfig = {},
  emptyMessage = 'No data available',
  className = '',
}: TableProps<T>) {
  const [localFilters, setLocalFilters] = useState<FilterConfig>(filterConfig);

  const handleSort = (key: keyof T) => {
    if (!onSort) return;

    const direction = 
      sortConfig?.key === key && sortConfig.direction === 'ASC' ? 'DESC' : 'ASC';
    
    onSort({ key, direction });
  };

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...localFilters, [key]: value || undefined };
    setLocalFilters(newFilters);
    onFilter?.(newFilters);
  };

  const getSortIcon = (key: keyof T) => {
    if (sortConfig?.key !== key) return '↕️';
    return sortConfig.direction === 'ASC' ? '↑' : '↓';
  };

  const filteredData = useMemo(() => {
    if (!Object.keys(localFilters).length) return data;

    return data.filter(row => {
      return Object.entries(localFilters).every(([key, filterValue]) => {
        if (!filterValue) return true;
        const cellValue = String(row[key]).toLowerCase();
        return cellValue.includes(String(filterValue).toLowerCase());
      });
    });
  }, [data, localFilters]);

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className={`${styles.tableContainer} ${className}`}>
      <table className={styles.table}>
        <thead>
          <tr>
            {columns.map((column) => (
              <th
                key={String(column.key)}
                className={`${styles.th} ${column.sortable ? styles.sortable : ''}`}
                style={{ width: column.width }}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                <div className={styles.headerContent}>
                  <span>{column.header}</span>
                  {column.sortable && (
                    <span className={styles.sortIcon}>
                      {getSortIcon(column.key)}
                    </span>
                  )}
                </div>
              </th>
            ))}
          </tr>
          <tr className={styles.filterRow}>
            {columns.map((column) => (
              <th key={`filter-${String(column.key)}`} className={styles.filterCell}>
                {column.filterable && (
                  <input
                    type="text"
                    placeholder={`Filter ${column.header.toLowerCase()}...`}
                    value={localFilters[String(column.key)] || ''}
                    onChange={(e) => handleFilterChange(String(column.key), e.target.value)}
                    className={styles.filterInput}
                  />
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {filteredData.length === 0 ? (
            <tr>
              <td colSpan={columns.length} className={styles.emptyCell}>
                {emptyMessage}
              </td>
            </tr>
          ) : (
            filteredData.map((row, index) => (
              <tr key={index} className={styles.row}>
                {columns.map((column) => (
                  <td key={String(column.key)} className={styles.td}>
                    {column.render
                      ? column.render(row[column.key], row)
                      : String(row[column.key] || '')}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
}
