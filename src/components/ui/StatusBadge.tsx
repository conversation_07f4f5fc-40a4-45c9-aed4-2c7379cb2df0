'use client';

import React from 'react';
import { UserStatus, UserRole } from '../../types/graphql';
import styles from './StatusBadge.module.css';

interface StatusBadgeProps {
  status: UserStatus | UserRole;
  type?: 'status' | 'role';
  className?: string;
}

export function StatusBadge({ status, type = 'status', className = '' }: StatusBadgeProps) {
  const getStatusConfig = (status: UserStatus | UserRole, type: string) => {
    if (type === 'status') {
      switch (status as UserStatus) {
        case UserStatus.ACTIVE:
          return { label: 'Active', variant: 'success' };
        case UserStatus.INACTIVE:
          return { label: 'Inactive', variant: 'warning' };
        case UserStatus.SUSPENDED:
          return { label: 'Suspended', variant: 'danger' };
        default:
          return { label: status, variant: 'default' };
      }
    } else {
      switch (status as UserRole) {
        case UserRole.ADMIN:
          return { label: 'Admin', variant: 'primary' };
        case UserRole.MODERATOR:
          return { label: 'Moderator', variant: 'info' };
        case UserRole.USER:
          return { label: 'User', variant: 'default' };
        default:
          return { label: status, variant: 'default' };
      }
    }
  };

  const { label, variant } = getStatusConfig(status, type);

  return (
    <span className={`${styles.badge} ${styles[variant]} ${className}`}>
      {label}
    </span>
  );
}
