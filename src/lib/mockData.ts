import { User, UserRole, UserStatus } from '../types/graphql';

export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE,
    department: 'Engineering',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z',
    lastLoginAt: '2024-01-25T09:15:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: UserRole.MODERATOR,
    status: UserStatus.ACTIVE,
    department: 'Marketing',
    createdAt: '2024-01-10T08:20:00Z',
    updatedAt: '2024-01-18T16:30:00Z',
    lastLoginAt: '2024-01-24T11:45:00Z',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: UserRole.USER,
    status: UserStatus.INACTIVE,
    department: 'Sales',
    createdAt: '2024-01-05T12:15:00Z',
    updatedAt: '2024-01-12T10:20:00Z',
    lastLoginAt: '2024-01-15T13:30:00Z',
  },
  {
    id: '4',
    name: 'Alice Brown',
    email: '<EMAIL>',
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    department: 'HR',
    createdAt: '2024-01-08T14:45:00Z',
    updatedAt: '2024-01-22T09:10:00Z',
    lastLoginAt: '2024-01-25T15:20:00Z',
  },
  {
    id: '5',
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    role: UserRole.USER,
    status: UserStatus.SUSPENDED,
    department: 'Finance',
    createdAt: '2024-01-12T11:30:00Z',
    updatedAt: '2024-01-19T13:45:00Z',
    lastLoginAt: '2024-01-20T10:15:00Z',
  },
  {
    id: '6',
    name: 'Diana Davis',
    email: '<EMAIL>',
    role: UserRole.MODERATOR,
    status: UserStatus.ACTIVE,
    department: 'Engineering',
    createdAt: '2024-01-03T09:20:00Z',
    updatedAt: '2024-01-21T12:30:00Z',
    lastLoginAt: '2024-01-25T08:45:00Z',
  },
  {
    id: '7',
    name: 'Edward Miller',
    email: '<EMAIL>',
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    department: 'Operations',
    createdAt: '2024-01-07T15:10:00Z',
    updatedAt: '2024-01-16T11:25:00Z',
    lastLoginAt: '2024-01-23T14:50:00Z',
  },
  {
    id: '8',
    name: 'Fiona Garcia',
    email: '<EMAIL>',
    role: UserRole.USER,
    status: UserStatus.INACTIVE,
    department: 'Legal',
    createdAt: '2024-01-14T13:40:00Z',
    updatedAt: '2024-01-17T15:55:00Z',
    lastLoginAt: '2024-01-18T12:10:00Z',
  },
  {
    id: '9',
    name: 'George Martinez',
    email: '<EMAIL>',
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE,
    department: 'IT',
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-23T16:20:00Z',
    lastLoginAt: '2024-01-25T17:30:00Z',
  },
  {
    id: '10',
    name: 'Helen Rodriguez',
    email: '<EMAIL>',
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    department: 'Customer Support',
    createdAt: '2024-01-09T12:25:00Z',
    updatedAt: '2024-01-20T10:40:00Z',
    lastLoginAt: '2024-01-24T16:15:00Z',
  },
];

// Helper function to simulate API delay
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions for development
export const mockApiService = {
  async getUsers(variables?: any) {
    await delay(500); // Simulate network delay
    
    let filteredUsers = [...mockUsers];
    
    // Apply filters
    if (variables?.filter) {
      const { name, email, role, status, department } = variables.filter;
      
      filteredUsers = filteredUsers.filter(user => {
        if (name && !user.name.toLowerCase().includes(name.toLowerCase())) return false;
        if (email && !user.email.toLowerCase().includes(email.toLowerCase())) return false;
        if (role && user.role !== role) return false;
        if (status && user.status !== status) return false;
        if (department && !user.department?.toLowerCase().includes(department.toLowerCase())) return false;
        return true;
      });
    }
    
    // Apply sorting
    if (variables?.sortBy) {
      const { sortBy, sortOrder = 'ASC' } = variables;
      filteredUsers.sort((a, b) => {
        const aVal = a[sortBy as keyof User];
        const bVal = b[sortBy as keyof User];
        
        if (aVal < bVal) return sortOrder === 'ASC' ? -1 : 1;
        if (aVal > bVal) return sortOrder === 'ASC' ? 1 : -1;
        return 0;
      });
    }
    
    // Apply pagination
    const page = variables?.page || 1;
    const pageSize = variables?.pageSize || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    return {
      users: {
        data: paginatedUsers,
        total: filteredUsers.length,
        page,
        pageSize,
      },
    };
  },
  
  async getUserById(id: string) {
    await delay(300);
    const user = mockUsers.find(u => u.id === id);
    if (!user) throw new Error('User not found');
    return { user };
  },
};
