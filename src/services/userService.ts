import { ApolloError } from '@apollo/client';
import { getClient } from '../lib/apollo-client-wrapper';
import {
  GET_USERS,
  GET_USER_BY_ID,
  CREATE_USER,
  UPDATE_USER,
  DELETE_USER,
  GET_USER_STATS,
} from '../graphql/queries';
import {
  User,
  UsersQueryResult,
  UsersQueryVariables,
  CreateUserInput,
  UpdateUserInput,
  ApiResponse,
} from '../types/graphql';

export class UserService {
  private static client = getClient();

  /**
   * Fetch users with pagination, sorting, and filtering
   */
  static async getUsers(variables?: UsersQueryVariables): Promise<UsersQueryResult> {
    try {
      const { data } = await this.client.query({
        query: GET_USERS,
        variables: {
          page: 1,
          pageSize: 10,
          sortBy: 'createdAt',
          sortOrder: 'DESC',
          ...variables,
        },
        fetchPolicy: 'cache-first',
      });

      return data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Fetch a single user by ID
   */
  static async getUserById(id: string): Promise<User> {
    try {
      const { data } = await this.client.query({
        query: GET_USER_BY_ID,
        variables: { id },
        fetchPolicy: 'cache-first',
      });

      return data.user;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Create a new user
   */
  static async createUser(input: CreateUserInput): Promise<User> {
    try {
      const { data } = await this.client.mutate({
        mutation: CREATE_USER,
        variables: { input },
        refetchQueries: [{ query: GET_USERS }],
      });

      return data.createUser;
    } catch (error) {
      console.error('Error creating user:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Update an existing user
   */
  static async updateUser(input: UpdateUserInput): Promise<User> {
    try {
      const { data } = await this.client.mutate({
        mutation: UPDATE_USER,
        variables: { input },
        refetchQueries: [{ query: GET_USERS }],
      });

      return data.updateUser;
    } catch (error) {
      console.error('Error updating user:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Delete a user
   */
  static async deleteUser(id: string): Promise<ApiResponse<boolean>> {
    try {
      const { data } = await this.client.mutate({
        mutation: DELETE_USER,
        variables: { id },
        refetchQueries: [{ query: GET_USERS }],
      });

      return data.deleteUser;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats() {
    try {
      const { data } = await this.client.query({
        query: GET_USER_STATS,
        fetchPolicy: 'cache-first',
      });

      return data.userStats;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Handle GraphQL and network errors
   */
  private static handleError(error: unknown): Error {
    if (error instanceof ApolloError) {
      if (error.networkError) {
        return new Error(`Network error: ${error.networkError.message}`);
      }
      
      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        const messages = error.graphQLErrors.map(err => err.message).join(', ');
        return new Error(`GraphQL error: ${messages}`);
      }
    }

    if (error instanceof Error) {
      return error;
    }

    return new Error('An unknown error occurred');
  }

  /**
   * Clear cache for users
   */
  static clearCache(): void {
    this.client.cache.evict({ fieldName: 'users' });
    this.client.cache.gc();
  }
}
