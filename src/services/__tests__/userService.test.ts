import { ApolloError } from '@apollo/client';
import { UserService } from '../userService';
import { UserRole, UserStatus } from '../../types/graphql';

// Mock the Apollo client
jest.mock('../../lib/apollo-client-wrapper', () => ({
  getClient: () => ({
    query: jest.fn(),
    mutate: jest.fn(),
    cache: {
      evict: jest.fn(),
      gc: jest.fn(),
    },
  }),
}));

const mockClient = require('../../lib/apollo-client-wrapper').getClient();

describe('UserService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUsers', () => {
    it('fetches users successfully', async () => {
      const mockResponse = {
        data: {
          users: {
            data: [
              {
                id: '1',
                name: '<PERSON>',
                email: '<EMAIL>',
                role: UserRole.USER,
                status: UserStatus.ACTIVE,
              },
            ],
            total: 1,
            page: 1,
            pageSize: 10,
          },
        },
      };

      mockClient.query.mockResolvedValue(mockResponse);

      const result = await UserService.getUsers();

      expect(result).toEqual(mockResponse.data);
      expect(mockClient.query).toHaveBeenCalledWith({
        query: expect.any(Object),
        variables: {
          page: 1,
          pageSize: 10,
          sortBy: 'createdAt',
          sortOrder: 'DESC',
        },
        fetchPolicy: 'cache-first',
      });
    });

    it('handles query with custom variables', async () => {
      const mockResponse = {
        data: {
          users: {
            data: [],
            total: 0,
            page: 2,
            pageSize: 25,
          },
        },
      };

      mockClient.query.mockResolvedValue(mockResponse);

      const variables = {
        page: 2,
        pageSize: 25,
        sortBy: 'name',
        sortOrder: 'ASC' as const,
        filter: { name: 'John' },
      };

      await UserService.getUsers(variables);

      expect(mockClient.query).toHaveBeenCalledWith({
        query: expect.any(Object),
        variables,
        fetchPolicy: 'cache-first',
      });
    });

    it('handles GraphQL errors', async () => {
      const apolloError = new ApolloError({
        graphQLErrors: [{ message: 'User not found' }],
      });

      mockClient.query.mockRejectedValue(apolloError);

      await expect(UserService.getUsers()).rejects.toThrow('GraphQL error: User not found');
    });

    it('handles network errors', async () => {
      const apolloError = new ApolloError({
        networkError: new Error('Network connection failed'),
      });

      mockClient.query.mockRejectedValue(apolloError);

      await expect(UserService.getUsers()).rejects.toThrow('Network error: Network connection failed');
    });
  });

  describe('getUserById', () => {
    it('fetches user by ID successfully', async () => {
      const mockUser = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
      };

      mockClient.query.mockResolvedValue({
        data: { user: mockUser },
      });

      const result = await UserService.getUserById('1');

      expect(result).toEqual(mockUser);
      expect(mockClient.query).toHaveBeenCalledWith({
        query: expect.any(Object),
        variables: { id: '1' },
        fetchPolicy: 'cache-first',
      });
    });
  });

  describe('createUser', () => {
    it('creates user successfully', async () => {
      const mockUser = {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
      };

      mockClient.mutate.mockResolvedValue({
        data: { createUser: mockUser },
      });

      const input = {
        name: 'John Doe',
        email: '<EMAIL>',
        role: UserRole.USER,
      };

      const result = await UserService.createUser(input);

      expect(result).toEqual(mockUser);
      expect(mockClient.mutate).toHaveBeenCalledWith({
        mutation: expect.any(Object),
        variables: { input },
        refetchQueries: [{ query: expect.any(Object) }],
      });
    });
  });

  describe('updateUser', () => {
    it('updates user successfully', async () => {
      const mockUser = {
        id: '1',
        name: 'John Updated',
        email: '<EMAIL>',
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
      };

      mockClient.mutate.mockResolvedValue({
        data: { updateUser: mockUser },
      });

      const input = {
        id: '1',
        name: 'John Updated',
      };

      const result = await UserService.updateUser(input);

      expect(result).toEqual(mockUser);
      expect(mockClient.mutate).toHaveBeenCalledWith({
        mutation: expect.any(Object),
        variables: { input },
        refetchQueries: [{ query: expect.any(Object) }],
      });
    });
  });

  describe('deleteUser', () => {
    it('deletes user successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'User deleted successfully',
      };

      mockClient.mutate.mockResolvedValue({
        data: { deleteUser: mockResponse },
      });

      const result = await UserService.deleteUser('1');

      expect(result).toEqual(mockResponse);
      expect(mockClient.mutate).toHaveBeenCalledWith({
        mutation: expect.any(Object),
        variables: { id: '1' },
        refetchQueries: [{ query: expect.any(Object) }],
      });
    });
  });

  describe('clearCache', () => {
    it('clears cache successfully', () => {
      UserService.clearCache();

      expect(mockClient.cache.evict).toHaveBeenCalledWith({ fieldName: 'users' });
      expect(mockClient.cache.gc).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('handles unknown errors', async () => {
      mockClient.query.mockRejectedValue('Unknown error');

      await expect(UserService.getUsers()).rejects.toThrow('An unknown error occurred');
    });

    it('handles regular Error objects', async () => {
      const error = new Error('Regular error');
      mockClient.query.mockRejectedValue(error);

      await expect(UserService.getUsers()).rejects.toThrow('Regular error');
    });
  });
});
