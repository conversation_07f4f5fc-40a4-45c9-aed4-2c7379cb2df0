'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Table, Column } from '../../components/ui/Table';
import { Pagination } from '../../components/ui/Pagination';
import { StatusBadge } from '../../components/ui/StatusBadge';
import { UserService } from '../../services/userService';
import {
  User,
  UserRole,
  UserStatus,
  SortConfig,
  FilterConfig,
  UsersQueryVariables,
} from '../../types/graphql';
import styles from './page.module.css';

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: 'createdAt',
    direction: 'DESC',
  });
  const [filterConfig, setFilterConfig] = useState<FilterConfig>({});

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const variables: UsersQueryVariables = {
        page: currentPage,
        pageSize,
        sortBy: String(sortConfig.key),
        sortOrder: sortConfig.direction,
        filter: {
          name: filterConfig.name as string,
          email: filterConfig.email as string,
          role: filterConfig.role as UserRole,
          status: filterConfig.status as UserStatus,
          department: filterConfig.department as string,
        },
      };

      const result = await UserService.getUsers(variables);
      setUsers(result.users.data);
      setTotalUsers(result.users.total);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, sortConfig, filterConfig]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleSort = (newSortConfig: SortConfig) => {
    setSortConfig(newSortConfig);
    setCurrentPage(1); // Reset to first page when sorting
  };

  const handleFilter = (newFilterConfig: FilterConfig) => {
    setFilterConfig(newFilterConfig);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const columns: Column<User>[] = [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      filterable: true,
      width: '200px',
    },
    {
      key: 'email',
      header: 'Email',
      sortable: true,
      filterable: true,
      width: '250px',
    },
    {
      key: 'role',
      header: 'Role',
      sortable: true,
      filterable: true,
      width: '120px',
      render: (value: UserRole) => <StatusBadge status={value} type="role" />,
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      filterable: true,
      width: '120px',
      render: (value: UserStatus) => <StatusBadge status={value} type="status" />,
    },
    {
      key: 'department',
      header: 'Department',
      sortable: true,
      filterable: true,
      width: '150px',
    },
    {
      key: 'createdAt',
      header: 'Created',
      sortable: true,
      width: '180px',
      render: (value: string) => formatDate(value),
    },
    {
      key: 'lastLoginAt',
      header: 'Last Login',
      sortable: true,
      width: '180px',
      render: (value: string | null) => value ? formatDate(value) : 'Never',
    },
  ];

  const totalPages = Math.ceil(totalUsers / pageSize);

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <h2>Error Loading Users</h2>
          <p>{error}</p>
          <button onClick={fetchUsers} className={styles.retryButton}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>User Management</h1>
        <p>Manage and view all users in the system</p>
      </div>

      <div className={styles.tableWrapper}>
        <Table
          data={users}
          columns={columns}
          loading={loading}
          onSort={handleSort}
          onFilter={handleFilter}
          sortConfig={sortConfig}
          filterConfig={filterConfig}
          emptyMessage="No users found"
        />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalUsers}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
}
