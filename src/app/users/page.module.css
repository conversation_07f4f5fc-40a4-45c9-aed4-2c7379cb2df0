.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  min-height: 100vh;
  background-color: #f9fafb;
}

.header {
  margin-bottom: 32px;
  text-align: center;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 8px;
}

.header p {
  font-size: 1.125rem;
  color: #6b7280;
  margin: 0;
}

.tableWrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 24px;
}

.error {
  text-align: center;
  padding: 48px 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.error h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 16px;
}

.error p {
  color: #6b7280;
  margin-bottom: 24px;
  font-size: 1rem;
}

.retryButton {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background-color: #2563eb;
}

.retryButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .tableWrapper {
    padding: 16px;
    border-radius: 8px;
  }
}
